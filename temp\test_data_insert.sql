-- =====================================================
-- 存储过程 rms_fx_yuliu_med 测试数据插入脚本
-- 版本: MySQL 5.7
-- 创建日期: 2025-08-25
-- 说明: 为存储过程测试创建完整的测试数据集
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 科室信息数据 (rms_itf_hos_spec)
-- =====================================================
INSERT INTO rms_itf_hos_spec (spec_code, spec_name, create_time) VALUES
('TEST_DEPT_001', 'TEST_内科', NOW()),
('TEST_DEPT_002', 'TEST_外科', NOW()),
('TEST_DEPT_003', 'TEST_儿科', NOW()),
('TEST_DEPT_004', 'TEST_妇科', NOW()),
('TEST_DEPT_005', 'TEST_眼科', NOW());

-- =====================================================
-- 2. 给药途径数据 (rms_itf_hos_admin_route)
-- =====================================================
INSERT INTO rms_itf_hos_admin_route (adm_code, adm_name, jzbs) VALUES
('TEST_ADM_001', 'TEST_口服', '0'),
('TEST_ADM_002', 'TEST_静脉注射', '0'),
('TEST_ADM_003', 'TEST_肌肉注射', '0'),
('TEST_ADM_004', 'TEST_外用', '0'),
('TEST_ADM_005', 'TEST_取药用', '1'),  -- 禁用途径
('TEST_ADM_006', 'TEST_遵医嘱', '1'),  -- 禁用途径
('TEST_ADM_007', 'TEST_自用', '1');    -- 禁用途径

-- =====================================================
-- 3. 频次数据 (rms_itf_hos_frequency)
-- =====================================================
INSERT INTO rms_itf_hos_frequency (freq_code, freq_name, freq_sp, daily_times, weekly_times, hosp_code, create_time) VALUES
('01', 'TEST_立即', '立即使用', 1.00, '1', 'TEST_HOSP', NOW()),
('02', 'TEST_每日一次', '每日一次', 1.00, '7', 'TEST_HOSP', NOW()),
('03', 'TEST_每日两次', '每日两次', 2.00, '14', 'TEST_HOSP', NOW()),
('04', 'TEST_每日三次', '每日三次', 3.00, '21', 'TEST_HOSP', NOW()),
('13', 'TEST_立即13', '立即使用13', 1.00, '1', 'TEST_HOSP', NOW()),
('14', 'TEST_立即14', '立即使用14', 1.00, '1', 'TEST_HOSP', NOW()),
('15', 'TEST_立即15', '立即使用15', 1.00, '1', 'TEST_HOSP', NOW());

-- =====================================================
-- 4. 基础药品数据 (rms_itf_hos_drug)
-- =====================================================
INSERT INTO rms_itf_hos_drug (
    drug_id, drug_code, drug_spec, packing_spec, packing_uom, packing_min_spec, 
    packing_min_qty, drug_qty, drug_uom, drug_manuf, drug_product_name, drug_name, 
    drug_sp, drug_general_name, drug_for_name, is_antibac, antitumor_type, zx_flag, 
    is_basic_drug, is_injection, law_type, drug_type, antibac_type, is_purchase_internet, 
    stop_flag, stop_date, medicare_code, medicare_type, medicare_remark, approval_certif, 
    hosp_code, imp_date, is_op_ip, is_rm, is_djm, dj, je, cancer_drug, cold_drug, 
    proton_pump_drug, glucocorticoid, key_drug, blood_drug, spirit_anaesthesia_drug, 
    is_zdjk, expensive_drug, is_tsyp, gt_20, gt_21, is_jc, create_time, update_time
) VALUES
-- 正常药品
(900001, 'TEST_DRUG_001', '10mg*10片', '1盒', '盒', '1片', 10, 10, '片', 'TEST_制药', 'TEST_阿司匹林片', 'TEST_阿司匹林', 'ASPIRIN', '阿司匹林', '片剂', '0', '', '1', '1', '0', '', '', '', '0', '0', NULL, 'TEST_001', '乙类', '', 'TEST_APPROVAL_001', 'TEST_HOSP', '2025-08-25', 'op', '0', '0', 5.50, 55.00, '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', NOW(), NOW()),

-- 停用药品
(900002, 'TEST_DRUG_002', '5mg*20片', '1盒', '盒', '1片', 20, 5, '片', 'TEST_制药', 'TEST_停用药品', 'TEST_停用药品', 'STOPPED', '停用药品', '片剂', '0', '', '3', '1', '0', '', '', '', '0', '1', '2025-08-25', 'TEST_002', '乙类', '', 'TEST_APPROVAL_002', 'TEST_HOSP', '2025-08-25', 'op', '0', '0', 3.20, 64.00, '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', NOW(), NOW()),

-- 软膏剂
(900003, 'TEST_DRUG_003', '10g', '1支', '支', '1g', 10, 10, 'g', 'TEST_制药', 'TEST_红霉素软膏', 'TEST_红霉素软膏', 'ERYTHROMYCIN', '红霉素', '软膏剂', '1', '', '1', '1', '0', '', '', '1', '0', '0', NULL, 'TEST_003', '乙类', '', 'TEST_APPROVAL_003', 'TEST_HOSP', '2025-08-25', 'op', '0', '0', 8.50, 85.00, '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', NOW(), NOW()),

-- 乳膏剂
(900004, 'TEST_DRUG_004', '15g', '1支', '支', '1g', 15, 15, 'g', 'TEST_制药', 'TEST_氢化可的松乳膏', 'TEST_氢化可的松乳膏', 'HYDROCORTISONE', '氢化可的松', '乳膏剂', '0', '', '1', '1', '0', '', '', '', '0', '0', NULL, 'TEST_004', '乙类', '', 'TEST_APPROVAL_004', 'TEST_HOSP', '2025-08-25', 'op', '0', '0', 12.50, 125.00, '0', '0', '0', '1', '0', '0', '0', '0', '0', '0', '0', '0', '0', NOW(), NOW()),

-- 滴眼剂
(900005, 'TEST_DRUG_005', '5ml', '1瓶', '瓶', '1ml', 5, 5, 'ml', 'TEST_制药', 'TEST_氯霉素滴眼液', 'TEST_氯霉素滴眼液', 'CHLORAMPHENICOL', '氯霉素', '滴眼剂', '1', '', '1', '1', '0', '', '', '1', '0', '0', NULL, 'TEST_005', '乙类', '', 'TEST_APPROVAL_005', 'TEST_HOSP', '2025-08-25', 'op', '0', '0', 6.80, 68.00, '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', NOW(), NOW()),

-- 注射剂（抗菌药物）
(900006, 'TEST_DRUG_006', '1g', '1支', '支', '1g', 1, 1, 'g', 'TEST_制药', 'TEST_头孢曲松钠注射液', 'TEST_头孢曲松钠注射', 'CEFTRIAXONE', '头孢曲松钠', '注射剂', '1', '', '1', '1', '1', '', '', '1', '0', '0', NULL, 'TEST_006', '乙类', '', 'TEST_APPROVAL_006', 'TEST_HOSP', '2025-08-25', 'both', '0', '0', 25.50, 255.00, '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', NOW(), NOW()),

-- 普通注射剂
(900007, 'TEST_DRUG_007', '2ml', '1支', '支', '1ml', 2, 2, 'ml', 'TEST_制药', 'TEST_维生素B1注射液', 'TEST_维生素B1注射', 'VB1', '维生素B1', '注射剂', '0', '', '1', '1', '1', '', '', '', '0', '0', NULL, 'TEST_007', '乙类', '', 'TEST_APPROVAL_007', 'TEST_HOSP', '2025-08-25', 'both', '0', '0', 3.20, 32.00, '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', NOW(), NOW()),

-- 普通口服药
(900008, 'TEST_DRUG_008', '25mg*30片', '1盒', '盒', '1片', 30, 25, '片', 'TEST_制药', 'TEST_卡托普利片', 'TEST_卡托普利', 'CAPTOPRIL', '卡托普利', '片剂', '0', '', '1', '1', '0', '', '', '', '0', '0', NULL, 'TEST_008', '乙类', '', 'TEST_APPROVAL_008', 'TEST_HOSP', '2025-08-25', 'op', '0', '0', 15.60, 156.00, '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', '0', NOW(), NOW());

-- =====================================================
-- 5. 药品标准数据映射 (rms_t_byyydzb)
-- =====================================================
INSERT INTO rms_t_byyydzb (akb020, yp_code, sda_id, cid) VALUES
('TEST_001', 'TEST_DRUG_001', 900001, 900001),
('TEST_002', 'TEST_DRUG_002', 900002, 900002),
('TEST_003', 'TEST_DRUG_003', 900003, 900003),
('TEST_004', 'TEST_DRUG_004', 900004, 900004),
('TEST_005', 'TEST_DRUG_005', 900005, 900005),
('TEST_006', 'TEST_DRUG_006', 900006, 900006),
('TEST_007', 'TEST_DRUG_007', 900007, 900007),
('TEST_008', 'TEST_DRUG_008', 900008, 900008);

-- =====================================================
-- 6. 药品最大发药量数据 (rms_t_drug_zdfyl)
-- =====================================================
INSERT INTO rms_t_drug_zdfyl (drug_code, drug_name, hosp_flag, zdfyl, unit, bz) VALUES
('TEST_DRUG_001', 'TEST_阿司匹林片', 'op', '30', '片', 'TEST_门诊最大30片'),
('TEST_DRUG_003', 'TEST_红霉素软膏', 'op', '3', '支', 'TEST_门诊最大3支'),
('TEST_DRUG_004', 'TEST_氢化可的松乳膏', 'op', '2', '支', 'TEST_门诊最大2支'),
('TEST_DRUG_005', 'TEST_氯霉素滴眼液', 'op', '2', '瓶', 'TEST_门诊最大2瓶'),
('TEST_DRUG_008', 'TEST_卡托普利片', 'op', '60', '片', 'TEST_门诊最大60片');

-- =====================================================
-- 7. 药品科室限制数据 (rms_t_med_zdy_dept)
-- =====================================================
INSERT INTO rms_t_med_zdy_dept (id, yp_code, dept_code, create_time) VALUES
(900001, 'TEST_DRUG_005', 'TEST_DEPT_005', NOW()),  -- 滴眼剂只能眼科使用
(900002, 'TEST_DRUG_006', 'TEST_DEPT_001', NOW()),  -- 抗菌药物只能内科使用
(900003, 'TEST_DRUG_006', 'TEST_DEPT_002', NOW());  -- 抗菌药物也可以外科使用

-- =====================================================
-- 8. 药品医生限制数据 (rms_t_med_zdy_doct)
-- =====================================================
INSERT INTO rms_t_med_zdy_doct (id, yp_code, doct_code, create_time) VALUES
(900001, 'TEST_DRUG_006', 'TEST_DOCT_001', NOW()),  -- 抗菌药物只能特定医生使用
(900002, 'TEST_DRUG_006', 'TEST_DOCT_002', NOW());

-- =====================================================
-- 9. 药品自定义给药途径数据 (rms_t_med_zdy_gytj)
-- =====================================================
INSERT INTO rms_t_med_zdy_gytj (id, yp_code, gytj_code, create_time) VALUES
(900001, 'TEST_DRUG_001', 'TEST_ADM_001', NOW()),  -- 阿司匹林只能口服
(900002, 'TEST_DRUG_003', 'TEST_ADM_004', NOW()),  -- 软膏只能外用
(900003, 'TEST_DRUG_006', 'TEST_ADM_002', NOW()),  -- 注射剂只能静脉注射
(900004, 'TEST_DRUG_006', 'TEST_ADM_003', NOW());  -- 注射剂也可以肌肉注射

-- =====================================================
-- 10. 标准数据常规量结果 (rms_t_sda_cgl_result)
-- =====================================================
INSERT INTO rms_t_sda_cgl_result (condition_id, sda_id, reco_type, yl_min, yl_max, yl_unit) VALUES
(900001, 900006, 2, 2.0, 4.0, '次/日'),  -- 头孢曲松钠推荐频次
(900002, 900001, 2, 1.0, 3.0, '次/日'),  -- 阿司匹林推荐频次
(900003, 900008, 2, 2.0, 3.0, '次/日');  -- 卡托普利推荐频次

-- =====================================================
-- 11. 相互作用数据 (rms_t_xhzy_edi)
-- =====================================================
INSERT INTO rms_t_xhzy_edi (
    id, yaowua, yaowub, effect, mechanism, relatedrug, reference, 
    imp_bs, jx_bs, recommandations, main, sjbs, gx, remark, sugflag, 
    type, significance, onset, documentation, effecttype, result, ispb
) VALUES
(900001, 900001, 900008, 'TEST_可能增加低血压风险', 'TEST_协同降压作用', 'TEST_阿司匹林+卡托普利', 'TEST_参考文献1', '1', '', 'TEST_监测血压', '', '', NULL, 'TEST_相互作用', 'xhzy', '', NULL, NULL, NULL, NULL, '', ''),
(900002, 900008, 900001, 'TEST_可能增加低血压风险', 'TEST_协同降压作用', 'TEST_卡托普利+阿司匹林', 'TEST_参考文献1', '1', '', 'TEST_监测血压', '', '', NULL, 'TEST_相互作用', 'xhzy', '', NULL, NULL, NULL, NULL, '', '');

-- =====================================================
-- 12. 处方数据 (rms_t_pres)
-- =====================================================
INSERT INTO rms_t_pres (
    code, hosp_code, dept_code, dept_name, doct_code, doct_name, doct_type,
    doct_type_name, his_time, hosp_flag, treat_type, treat_code, bed_no,
    name, birth, sex, weight, height, id_card, medical_record, card_type,
    card_code, pregnant_unit, pregnant, all_info, dia_info, pres_id, reason,
    is_urgent, is_new, is_current, pres_type, pres_time, discharge_drug,
    adm_type, requir, cs1, ts, solvent, jl, cs2, lb, fs1, fs2,
    prescription_type, level, flag, is_read_doc, is_read_ys, text, zyzb,
    zyzb_code, zyzz, zyzz_code, reason1, pres_sm, reason2, create_time, update_time
) VALUES
-- 门诊处方 - 正常
('TEST_PRES_001', 'TEST_HOSP', 'TEST_DEPT_001', 'TEST_内科', 'TEST_DOCT_001', 'TEST_张医生', '1', '主治医师', '2025-08-25 10:00:00', 'op', '1', 'TEST_TREAT_001', '', 'TEST_患者1', '1980-01-01', '1', 70.0, 170.0, '110101198001011234', 'TEST_MR_001', '1', 'TEST_CARD_001', '', '', '', '', 'TEST_PRES_ID_001', '', '0', '1', '1', 'R', '2025-08-25 10:00:00', '0', '', '', '', '', '', '', '', '', '', '', '1', '', 0, '0', '0', '', '', '', '', '', '', '', '', NOW(), NOW()),

-- 门诊处方 - 不同科室
('TEST_PRES_002', 'TEST_HOSP', 'TEST_DEPT_002', 'TEST_外科', 'TEST_DOCT_002', 'TEST_李医生', '2', '副主任医师', '2025-08-25 11:00:00', 'op', '1', 'TEST_TREAT_002', '', 'TEST_患者2', '1990-05-15', '2', 60.0, 165.0, '110101199005151234', 'TEST_MR_002', '1', 'TEST_CARD_002', '', '', '', '', 'TEST_PRES_ID_002', '', '0', '1', '1', 'R', '2025-08-25 11:00:00', '0', '', '', '', '', '', '', '', '', '', '', '1', '', 0, '0', '0', '', '', '', '', '', '', '', '', NOW(), NOW()),

-- 门诊处方 - 眼科
('TEST_PRES_003', 'TEST_HOSP', 'TEST_DEPT_005', 'TEST_眼科', 'TEST_DOCT_003', 'TEST_王医生', '1', '主治医师', '2025-08-25 12:00:00', 'op', '1', 'TEST_TREAT_003', '', 'TEST_患者3', '1985-12-20', '1', 75.0, 175.0, '110101198512201234', 'TEST_MR_003', '1', 'TEST_CARD_003', '', '', '', '', 'TEST_PRES_ID_003', '', '0', '1', '1', 'R', '2025-08-25 12:00:00', '0', '', '', '', '', '', '', '', '', '', '', '1', '', 0, '0', '0', '', '', '', '', '', '', '', '', NOW(), NOW()),

-- 住院处方 - 临时医嘱
('TEST_PRES_004', 'TEST_HOSP', 'TEST_DEPT_001', 'TEST_内科', 'TEST_DOCT_001', 'TEST_张医生', '1', '主治医师', '2025-08-25 13:00:00', 'ip', '2', 'TEST_TREAT_004', '001', 'TEST_患者4', '1975-08-10', '2', 65.0, 160.0, '110101197508101234', 'TEST_MR_004', '1', 'TEST_CARD_004', '', '', '', '', 'TEST_PRES_ID_004', '', '0', '1', '1', 'T', '2025-08-25 13:00:00', '0', '', '', '', '', '', '', '', '', '', '', '1', '', 0, '0', '0', '', '', '', '', '', '', '', '', NOW(), NOW()),

-- 门诊处方 - 无权限科室
('TEST_PRES_005', 'TEST_HOSP', 'TEST_DEPT_003', 'TEST_儿科', 'TEST_DOCT_004', 'TEST_赵医生', '1', '主治医师', '2025-08-25 14:00:00', 'op', '1', 'TEST_TREAT_005', '', 'TEST_患者5', '2010-03-15', '1', 30.0, 120.0, '110101201003151234', 'TEST_MR_005', '1', 'TEST_CARD_005', '', '', '', '', 'TEST_PRES_ID_005', '', '0', '1', '1', 'R', '2025-08-25 14:00:00', '0', '', '', '', '', '', '', '', '', '', '', '1', '', 0, '0', '0', '', '', '', '', '', '', '', '', NOW(), NOW()),

-- 历史处方 - 用于相互作用测试
('TEST_PRES_006', 'TEST_HOSP', 'TEST_DEPT_001', 'TEST_内科', 'TEST_DOCT_001', 'TEST_张医生', '1', '主治医师', '2025-08-24 10:00:00', 'op', '1', 'TEST_TREAT_006', '', 'TEST_患者1', '1980-01-01', '1', 70.0, 170.0, '110101198001011234', 'TEST_MR_001', '1', 'TEST_CARD_001', '', '', '', '', 'TEST_PRES_ID_006', '', '0', '1', '1', 'R', '2025-08-24 10:00:00', '0', '', '', '', '', '', '', '', '', '', '', '1', '', 1, '0', '0', '', '', '', '', '', '', '', '', NOW(), NOW());

-- =====================================================
-- 13. 处方药品数据 (rms_t_pres_med)
-- =====================================================
INSERT INTO rms_t_pres_med (
    code, med_name, his_code, insur_code, approval, spec, `group`, reason,
    dose_unit, dose, ord_qty, ord_uom, freq, administer, begin_time, end_time,
    days, decoction_code, money, pres_id, med_reason1, yysm, bz
) VALUES
-- 正常用药 - 阿司匹林
('TEST_PRES_001', 'TEST_阿司匹林片', 'TEST_DRUG_001', 'TEST_001', 'TEST_APPROVAL_001', '10mg*10片', '1', '', '片', '1', '20', '片', '02', 'TEST_ADM_001', '', '', '10', '', '11.00', 'TEST_PRES_ID_001', '', '', ''),

-- 超量用药 - 阿司匹林
('TEST_PRES_002', 'TEST_阿司匹林片', 'TEST_DRUG_001', 'TEST_001', 'TEST_APPROVAL_001', '10mg*10片', '1', '', '片', '1', '50', '片', '02', 'TEST_ADM_001', '', '', '25', '', '27.50', 'TEST_PRES_ID_002', '', '', ''),

-- 软膏超量 - 红霉素软膏
('TEST_PRES_002', 'TEST_红霉素软膏', 'TEST_DRUG_003', 'TEST_003', 'TEST_APPROVAL_003', '10g', '2', '', 'g', '适量', '6', '支', '02', 'TEST_ADM_004', '', '', '7', '', '51.00', 'TEST_PRES_ID_002', '', '', ''),

-- 滴眼剂 - 正确科室
('TEST_PRES_003', 'TEST_氯霉素滴眼液', 'TEST_DRUG_005', 'TEST_005', 'TEST_APPROVAL_005', '5ml', '1', '', '滴', '1-2', '1', '瓶', '04', 'TEST_ADM_004', '', '', '7', '', '6.80', 'TEST_PRES_ID_003', '', '', ''),

-- 抗菌药物 - 错误科室
('TEST_PRES_005', 'TEST_头孢曲松钠注射液', 'TEST_DRUG_006', 'TEST_006', 'TEST_APPROVAL_006', '1g', '1', '', 'g', '1', '5', '支', '02', 'TEST_ADM_002', '', '', '5', '', '127.50', 'TEST_PRES_ID_005', '', '', ''),

-- 注射剂 - 立即使用频次
('TEST_PRES_001', 'TEST_维生素B1注射液', 'TEST_DRUG_007', 'TEST_007', 'TEST_APPROVAL_007', '2ml', '3', '', 'ml', '2', '1', '支', '01', 'TEST_ADM_002', '', '', '1', '', '3.20', 'TEST_PRES_ID_001', '', '', ''),

-- 住院临时医嘱 - 立即使用
('TEST_PRES_004', 'TEST_头孢曲松钠注射液', 'TEST_DRUG_006', 'TEST_006', 'TEST_APPROVAL_006', '1g', '1', '', 'g', '1', '2', '支', '01', 'TEST_ADM_002', '2025-08-25 13:00:00', '2025-08-25 13:30:00', '', '', '51.00', 'TEST_PRES_ID_004', '', '', ''),

-- 禁用给药途径
('TEST_PRES_002', 'TEST_卡托普利片', 'TEST_DRUG_008', 'TEST_008', 'TEST_APPROVAL_008', '25mg*30片', '4', '', '片', '1', '30', '片', '02', 'TEST_ADM_005', '', '', '15', '', '15.60', 'TEST_PRES_ID_002', '', '', ''),

-- 错误给药途径 - 口服药用注射途径
('TEST_PRES_002', 'TEST_阿司匹林片', 'TEST_DRUG_001', 'TEST_001', 'TEST_APPROVAL_001', '10mg*10片', '5', '', '片', '1', '10', '片', '02', 'TEST_ADM_002', '', '', '5', '', '5.50', 'TEST_PRES_ID_002', '', '', ''),

-- 历史处方药品 - 用于相互作用测试
('TEST_PRES_006', 'TEST_卡托普利片', 'TEST_DRUG_008', 'TEST_008', 'TEST_APPROVAL_008', '25mg*30片', '1', '', '片', '1', '30', '片', '02', 'TEST_ADM_001', '', '', '15', '', '15.60', 'TEST_PRES_ID_006', '', '', '');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 插入完成提示
SELECT '测试数据插入完成！' AS message;
