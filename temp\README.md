# rms_fx_yuliu_med 存储过程测试方案

## 项目概述

本项目为合理用药系统中的 `rms_fx_yuliu_med` 存储过程提供了完整的测试方案。该存储过程负责药品余留/用药总量分析，是合理用药审查的核心组件之一。

## 快速开始

### 1. 环境要求
- MySQL 5.7 或更高版本
- 具有存储过程执行权限的数据库用户
- 建议在测试环境中执行

### 2. 快速执行
```bash
# 1. 插入测试数据
mysql -u username -p database_name < test_data_insert.sql

# 2. 执行测试
mysql -u username -p database_name < test_execution_script.sql

# 3. 清理测试数据
mysql -u username -p database_name < test_data_cleanup.sql
```

### 3. 查看结果
测试执行后会自动显示测试结果统计和详细信息。

## 文件结构

```
temp/
├── README.md                    # 项目说明文档
├── 测试文档说明.md               # 详细测试文档
├── test_data_insert.sql         # 测试数据插入脚本
├── positive_test_cases.sql      # 正向测试用例
├── negative_test_cases.sql      # 负向测试用例
├── test_execution_script.sql    # 完整测试执行脚本
└── test_data_cleanup.sql        # 测试数据清理脚本
```

## 测试覆盖范围

### 功能模块覆盖
- ✅ 药品停用状态检查
- ✅ 门诊用药总量检查
- ✅ 外用软膏最大发药量检查
- ✅ 药品科室使用限制检查
- ✅ 药品医生使用限制检查
- ✅ 给药途径合规性检查
- ✅ 住院临时医嘱频次分析
- ✅ 门诊注射剂频次分析
- ✅ 抗菌药物频次特殊处理
- ✅ 历史处方相互作用检查

### 测试类型覆盖
- ✅ 正向测试（7个用例）
- ✅ 负向测试（9个用例）
- ✅ 边界值测试
- ✅ 异常处理测试

## 测试数据设计

### 数据规模
- 基础药品数据：8条
- 处方数据：6条
- 处方药品数据：10条
- 配置数据：30+条
- 总计：50+条测试数据

### 数据特点
- 使用TEST_前缀避免与生产数据冲突
- 主键从900000开始确保唯一性
- 覆盖所有业务场景和异常情况
- 数据关系完整，支持复杂查询

## 测试用例说明

### 正向测试用例
| 编号 | 测试场景 | 预期结果 |
|------|----------|----------|
| P001 | 正常药品检查流程 | 无警告信息 |
| P002 | 滴眼剂在眼科使用 | 无科室限制警告 |
| P003 | 有权限医生使用抗菌药物 | 无医生权限警告 |
| P004 | 正确给药途径使用 | 无给药途径警告 |
| P005 | 合规用药总量 | 无用药总量警告 |
| P006 | 住院临时医嘱频次处理 | 正确删除频次警告 |
| P007 | 门诊注射剂频次处理 | 正确删除频次警告 |

### 负向测试用例
| 编号 | 测试场景 | 预期结果 |
|------|----------|----------|
| N001 | 停用药品检查 | 直接退出，无分析结果 |
| N002 | 超量用药检查 | 产生用药总量警告 |
| N003 | 软膏超量检查 | 产生软膏超量警告 |
| N004 | 科室限制检查 | 产生科室限制警告 |
| N005 | 医生限制检查 | 产生医生限制警告 |
| N006 | 禁用给药途径检查 | 产生给药途径错误警告 |
| N007 | 自定义给药途径限制 | 产生给药途径问题警告 |
| N008 | 历史相互作用检查 | 产生相互作用警告 |
| N009 | 边界值测试 | 5支软膏不产生警告 |

## 执行结果示例

```
=== 测试结果汇总 ===
测试类型    总用例数  通过数  失败数  通过率
正向测试    7        7       0       100.00%
负向测试    9        9       0       100.00%
总计        16       16      0       100.00%
```

## 安全注意事项

### ⚠️ 重要警告
1. **强烈建议在测试环境执行**
2. **执行前请备份重要数据**
3. **测试完成后务必执行清理脚本**

### 数据安全
- 测试数据使用特殊标识，与生产数据隔离
- 清理脚本会完全删除所有测试相关数据
- 不会影响现有的生产数据

## 故障排除

### 常见问题及解决方案

#### 1. 存储过程不存在
```
错误：PROCEDURE database.rms_fx_yuliu_med doesn't exist
解决：请先部署存储过程到数据库
```

#### 2. 权限不足
```
错误：Access denied for user 'username'@'host'
解决：确保数据库用户有足够的权限
```

#### 3. 外键约束错误
```
错误：Cannot add or update a child row
解决：检查相关表的数据完整性
```

#### 4. 测试数据冲突
```
错误：Duplicate entry for key 'PRIMARY'
解决：先执行清理脚本，再重新插入测试数据
```

## 性能基准

### 执行时间参考
- 单个测试用例：< 100ms
- 完整测试套件：< 5秒
- 数据插入：< 1秒
- 数据清理：< 1秒

### 资源消耗
- 内存使用：< 10MB
- 临时表空间：< 1MB
- 测试数据大小：< 100KB

## 扩展和定制

### 添加新测试用例
1. 在相应的测试文件中添加测试逻辑
2. 更新测试执行脚本
3. 更新测试文档

### 修改测试数据
1. 编辑 `test_data_insert.sql`
2. 确保数据关系完整性
3. 更新清理脚本

### 自定义验证逻辑
1. 修改测试执行脚本中的验证SQL
2. 添加新的结果检查逻辑
3. 更新预期结果

## 版本历史

| 版本 | 日期 | 变更说明 |
|------|------|----------|
| 1.0 | 2025-08-25 | 初始版本，完整测试方案 |

## 贡献指南

### 提交问题
如发现问题，请提供以下信息：
- 数据库版本
- 错误信息
- 执行步骤
- 环境信息

### 改进建议
欢迎提出改进建议：
- 新的测试场景
- 性能优化
- 文档完善
- 工具改进

## 联系信息

- 项目维护：测试团队
- 技术支持：数据库管理员
- 文档更新：开发团队

---

**最后更新：2025-08-25**
**文档版本：1.0**
**适用环境：MySQL 5.7+**
