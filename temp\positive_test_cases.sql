-- =====================================================
-- 存储过程 rms_fx_yuliu_med 正向测试用例
-- 版本: MySQL 5.7
-- 创建日期: 2025-08-25
-- 说明: 测试存储过程在正常情况下的功能表现
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;

-- =====================================================
-- 正向测试用例 1: 正常药品检查流程
-- 测试场景: 正常药品，合规用药总量，正确权限
-- 预期结果: 不产生任何警告信息
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_001';

-- 执行存储过程
CALL rms_fx_yuliu_med('TEST_PRES_001', 'TEST_DRUG_001');

-- 验证结果：应该没有警告信息
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS - 正常药品检查无警告'
        ELSE CONCAT('FAIL - 发现', COUNT(*), '条警告信息')
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' AND ywa = 'TEST_阿司匹林片';

-- 显示详细结果（如果有警告）
SELECT '=== 正向测试用例1详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' AND ywa = 'TEST_阿司匹林片';

-- =====================================================
-- 正向测试用例 2: 滴眼剂在正确科室使用
-- 测试场景: 滴眼剂在眼科使用，符合科室限制
-- 预期结果: 不产生科室限制警告
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_003';

-- 执行存储过程
CALL rms_fx_yuliu_med('TEST_PRES_003', 'TEST_DRUG_005');

-- 验证结果：应该没有科室限制警告
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS - 滴眼剂在眼科使用无科室限制警告'
        ELSE CONCAT('FAIL - 发现', COUNT(*), '条科室限制警告')
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_003' 
  AND ywa = 'TEST_氯霉素滴眼液' 
  AND wtcode = 'RLT048';

-- 显示详细结果
SELECT '=== 正向测试用例2详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_003' AND ywa = 'TEST_氯霉素滴眼液';

-- =====================================================
-- 正向测试用例 3: 抗菌药物在有权限医生处方
-- 测试场景: 抗菌药物由有权限的医生开具
-- 预期结果: 不产生医生权限警告
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_001';

-- 执行存储过程（TEST_DOCT_001有权限使用TEST_DRUG_006）
CALL rms_fx_yuliu_med('TEST_PRES_001', 'TEST_DRUG_006');

-- 验证结果：应该没有医生权限警告
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS - 抗菌药物在有权限医生处方无警告'
        ELSE CONCAT('FAIL - 发现', COUNT(*), '条医生权限警告')
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' 
  AND ywa = 'TEST_头孢曲松钠注射液' 
  AND wtcode = 'RLT049';

-- 显示详细结果
SELECT '=== 正向测试用例3详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' AND ywa = 'TEST_头孢曲松钠注射液';

-- =====================================================
-- 正向测试用例 4: 正确给药途径使用
-- 测试场景: 药品使用符合自定义给药途径限制
-- 预期结果: 不产生给药途径警告
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_001';

-- 执行存储过程（阿司匹林口服给药）
CALL rms_fx_yuliu_med('TEST_PRES_001', 'TEST_DRUG_001');

-- 验证结果：应该没有给药途径警告
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS - 正确给药途径无警告'
        ELSE CONCAT('FAIL - 发现', COUNT(*), '条给药途径警告')
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' 
  AND ywa = 'TEST_阿司匹林片' 
  AND wtcode IN ('RLT003', 'RLT026');

-- 显示详细结果
SELECT '=== 正向测试用例4详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' AND ywa = 'TEST_阿司匹林片';

-- =====================================================
-- 正向测试用例 5: 合规用药总量
-- 测试场景: 门诊用药总量在最大发药量范围内
-- 预期结果: 不产生用药总量警告
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_001';

-- 执行存储过程（阿司匹林20片，最大30片）
CALL rms_fx_yuliu_med('TEST_PRES_001', 'TEST_DRUG_001');

-- 验证结果：应该没有用药总量警告
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS - 合规用药总量无警告'
        ELSE CONCAT('FAIL - 发现', COUNT(*), '条用药总量警告')
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' 
  AND ywa = 'TEST_阿司匹林片' 
  AND wtcode = 'RLT047';

-- 显示详细结果
SELECT '=== 正向测试用例5详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' AND ywa = 'TEST_阿司匹林片';

-- =====================================================
-- 正向测试用例 6: 住院临时医嘱频次处理
-- 测试场景: 住院临时医嘱使用立即频次
-- 预期结果: 正确处理频次，删除不必要的频次警告
-- =====================================================

-- 先插入一个频次警告（模拟之前的分析结果）
INSERT INTO rms_t_pres_fx (code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text)
VALUES ('TEST_PRES_004', 'TEST_头孢曲松钠注射液', '', '1', '一般提示', 'RLT030', 'PCWT', '频次问题', '频次不合理', '测试频次警告', 0, '频次问题');

-- 执行存储过程
CALL rms_fx_yuliu_med('TEST_PRES_004', 'TEST_DRUG_006');

-- 验证结果：频次警告应该被删除
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS - 住院临时医嘱频次警告已删除'
        ELSE CONCAT('FAIL - 仍有', COUNT(*), '条频次警告')
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_004' 
  AND ywa = 'TEST_头孢曲松钠注射液' 
  AND wtcode = 'RLT030'
  AND title LIKE '%频次%';

-- 显示详细结果
SELECT '=== 正向测试用例6详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_004' AND ywa = 'TEST_头孢曲松钠注射液';

-- =====================================================
-- 正向测试用例 7: 门诊注射剂立即使用频次
-- 测试场景: 门诊注射剂使用立即频次
-- 预期结果: 正确处理频次，删除频次警告
-- =====================================================

-- 先插入一个频次警告
INSERT INTO rms_t_pres_fx (code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text)
VALUES ('TEST_PRES_001', 'TEST_维生素B1注射', '', '1', '一般提示', 'RLT030', 'PCWT', '频次问题', '频次不合理', '测试频次警告', 0, '频次问题');

-- 执行存储过程
CALL rms_fx_yuliu_med('TEST_PRES_001', 'TEST_DRUG_007');

-- 验证结果：频次警告应该被删除
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS - 门诊注射剂频次警告已删除'
        ELSE CONCAT('FAIL - 仍有', COUNT(*), '条频次警告')
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' 
  AND ywa = 'TEST_维生素B1注射' 
  AND wtcode = 'RLT030'
  AND title LIKE '%频次%';

-- 显示详细结果
SELECT '=== 正向测试用例7详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' AND ywa = 'TEST_维生素B1注射';

-- =====================================================
-- 正向测试用例总结
-- =====================================================
SELECT '=== 正向测试用例总结 ===' AS section;
SELECT 
    '正向测试用例执行完成' AS message,
    '以上测试验证了存储过程在正常情况下的功能表现' AS description;
