-- =====================================================
-- 存储过程 rms_fx_yuliu_med 完整测试执行脚本
-- 版本: MySQL 5.7
-- 创建日期: 2025-08-25
-- 说明: 完整的测试执行脚本，包含测试环境准备、测试执行、结果验证
-- =====================================================

-- 设置字符集和会话参数
SET NAMES utf8mb4;
SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- =====================================================
-- 测试执行开始
-- =====================================================
SELECT '========================================' AS separator;
SELECT 'rms_fx_yuliu_med 存储过程测试开始' AS test_start;
SELECT CONCAT('测试开始时间: ', NOW()) AS start_time;
SELECT '========================================' AS separator;

-- =====================================================
-- 第一步：检查存储过程是否存在
-- =====================================================
SELECT '=== 第一步：检查存储过程是否存在 ===' AS step;

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS - 存储过程 rms_fx_yuliu_med 存在'
        ELSE 'FAIL - 存储过程 rms_fx_yuliu_med 不存在'
    END AS procedure_check
FROM information_schema.ROUTINES 
WHERE ROUTINE_SCHEMA = DATABASE() 
  AND ROUTINE_NAME = 'rms_fx_yuliu_med' 
  AND ROUTINE_TYPE = 'PROCEDURE';

-- =====================================================
-- 第二步：检查测试数据是否已准备
-- =====================================================
SELECT '=== 第二步：检查测试数据是否已准备 ===' AS step;

-- 检查药品数据
SELECT 
    CASE 
        WHEN COUNT(*) >= 8 THEN CONCAT('PASS - 测试药品数据已准备 (', COUNT(*), '条)')
        ELSE CONCAT('FAIL - 测试药品数据不足 (', COUNT(*), '条)')
    END AS drug_data_check
FROM rms_itf_hos_drug 
WHERE drug_code LIKE 'TEST_DRUG_%';

-- 检查处方数据
SELECT 
    CASE 
        WHEN COUNT(*) >= 6 THEN CONCAT('PASS - 测试处方数据已准备 (', COUNT(*), '条)')
        ELSE CONCAT('FAIL - 测试处方数据不足 (', COUNT(*), '条)')
    END AS prescription_data_check
FROM rms_t_pres 
WHERE code LIKE 'TEST_PRES_%';

-- 检查处方药品数据
SELECT 
    CASE 
        WHEN COUNT(*) >= 10 THEN CONCAT('PASS - 测试处方药品数据已准备 (', COUNT(*), '条)')
        ELSE CONCAT('FAIL - 测试处方药品数据不足 (', COUNT(*), '条)')
    END AS prescription_med_data_check
FROM rms_t_pres_med 
WHERE code LIKE 'TEST_PRES_%';

-- =====================================================
-- 第三步：清理之前的测试结果
-- =====================================================
SELECT '=== 第三步：清理之前的测试结果 ===' AS step;

DELETE FROM rms_t_pres_fx WHERE code LIKE 'TEST_PRES_%';

SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条之前的测试结果') AS cleanup_result;

-- =====================================================
-- 第四步：执行正向测试用例
-- =====================================================
SELECT '=== 第四步：执行正向测试用例 ===' AS step;

-- 创建测试结果统计表
DROP TEMPORARY TABLE IF EXISTS test_results;
CREATE TEMPORARY TABLE test_results (
    test_case VARCHAR(100),
    test_type VARCHAR(20),
    expected_result VARCHAR(100),
    actual_result VARCHAR(100),
    status VARCHAR(10)
);

-- 正向测试用例 1: 正常药品检查
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_001';
CALL rms_fx_yuliu_med('TEST_PRES_001', 'TEST_DRUG_001');

INSERT INTO test_results 
SELECT 
    '正常药品检查流程',
    '正向测试',
    '无警告信息',
    CASE WHEN COUNT(*) = 0 THEN '无警告信息' ELSE CONCAT(COUNT(*), '条警告') END,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' AND ywa = 'TEST_阿司匹林片';

-- 正向测试用例 2: 滴眼剂在正确科室使用
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_003';
CALL rms_fx_yuliu_med('TEST_PRES_003', 'TEST_DRUG_005');

INSERT INTO test_results 
SELECT 
    '滴眼剂正确科室使用',
    '正向测试',
    '无科室限制警告',
    CASE WHEN COUNT(*) = 0 THEN '无科室限制警告' ELSE CONCAT(COUNT(*), '条科室警告') END,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_003' AND ywa = 'TEST_氯霉素滴眼液' AND wtcode = 'RLT048';

-- 正向测试用例 3: 抗菌药物有权限医生
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_001';
CALL rms_fx_yuliu_med('TEST_PRES_001', 'TEST_DRUG_006');

INSERT INTO test_results 
SELECT 
    '抗菌药物有权限医生',
    '正向测试',
    '无医生权限警告',
    CASE WHEN COUNT(*) = 0 THEN '无医生权限警告' ELSE CONCAT(COUNT(*), '条医生警告') END,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' AND ywa = 'TEST_头孢曲松钠注射液' AND wtcode = 'RLT049';

-- =====================================================
-- 第五步：执行负向测试用例
-- =====================================================
SELECT '=== 第五步：执行负向测试用例 ===' AS step;

-- 负向测试用例 1: 停用药品检查
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_002';
CALL rms_fx_yuliu_med('TEST_PRES_002', 'TEST_DRUG_002');

INSERT INTO test_results 
SELECT 
    '停用药品检查',
    '负向测试',
    '无分析结果',
    CASE WHEN COUNT(*) = 0 THEN '无分析结果' ELSE CONCAT(COUNT(*), '条结果') END,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002';

-- 负向测试用例 2: 超量用药检查
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_002';
CALL rms_fx_yuliu_med('TEST_PRES_002', 'TEST_DRUG_001');

INSERT INTO test_results 
SELECT 
    '超量用药检查',
    '负向测试',
    '产生用药总量警告',
    CASE WHEN COUNT(*) > 0 THEN '产生用药总量警告' ELSE '无警告' END,
    CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002' AND ywa = 'TEST_阿司匹林片' AND wtcode = 'RLT047';

-- 负向测试用例 3: 软膏超量检查
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_002';
CALL rms_fx_yuliu_med('TEST_PRES_002', 'TEST_DRUG_003');

INSERT INTO test_results 
SELECT 
    '软膏超量检查',
    '负向测试',
    '产生软膏超量警告',
    CASE WHEN COUNT(*) > 0 THEN '产生软膏超量警告' ELSE '无警告' END,
    CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002' AND ywa = 'TEST_红霉素软膏' AND wtcode = 'RLT047';

-- 负向测试用例 4: 科室限制检查
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_005';
CALL rms_fx_yuliu_med('TEST_PRES_005', 'TEST_DRUG_005');

INSERT INTO test_results 
SELECT 
    '科室限制检查',
    '负向测试',
    '产生科室限制警告',
    CASE WHEN COUNT(*) > 0 THEN '产生科室限制警告' ELSE '无警告' END,
    CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_005' AND ywa = 'TEST_氯霉素滴眼液' AND wtcode = 'RLT048';

-- 负向测试用例 5: 医生限制检查
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_005';
CALL rms_fx_yuliu_med('TEST_PRES_005', 'TEST_DRUG_006');

INSERT INTO test_results 
SELECT 
    '医生限制检查',
    '负向测试',
    '产生医生限制警告',
    CASE WHEN COUNT(*) > 0 THEN '产生医生限制警告' ELSE '无警告' END,
    CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_005' AND ywa = 'TEST_头孢曲松钠注射液' AND wtcode = 'RLT049';

-- 负向测试用例 6: 禁用给药途径检查
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_002';
CALL rms_fx_yuliu_med('TEST_PRES_002', 'TEST_DRUG_008');

INSERT INTO test_results 
SELECT 
    '禁用给药途径检查',
    '负向测试',
    '产生给药途径错误警告',
    CASE WHEN COUNT(*) > 0 THEN '产生给药途径错误警告' ELSE '无警告' END,
    CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002' AND wtcode = 'RLT003';

-- 负向测试用例 7: 自定义给药途径限制
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_002';
CALL rms_fx_yuliu_med('TEST_PRES_002', 'TEST_DRUG_001');

INSERT INTO test_results 
SELECT 
    '自定义给药途径限制',
    '负向测试',
    '产生给药途径问题警告',
    CASE WHEN COUNT(*) > 0 THEN '产生给药途径问题警告' ELSE '无警告' END,
    CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002' AND ywa = 'TEST_阿司匹林片' AND wtcode = 'RLT026';

-- 负向测试用例 8: 历史相互作用检查
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_001';
CALL rms_fx_yuliu_med('TEST_PRES_001', 'TEST_DRUG_001');

INSERT INTO test_results 
SELECT 
    '历史相互作用检查',
    '负向测试',
    '产生相互作用警告',
    CASE WHEN COUNT(*) > 0 THEN '产生相互作用警告' ELSE '无警告' END,
    CASE WHEN COUNT(*) > 0 THEN 'PASS' ELSE 'FAIL' END
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' AND ywa = 'TEST_阿司匹林片' AND wtcode = 'RLT014';

-- =====================================================
-- 第六步：显示测试结果汇总
-- =====================================================
SELECT '=== 第六步：测试结果汇总 ===' AS step;

-- 显示所有测试结果
SELECT 
    test_case AS '测试用例',
    test_type AS '测试类型',
    expected_result AS '预期结果',
    actual_result AS '实际结果',
    status AS '状态'
FROM test_results
ORDER BY test_type, test_case;

-- 统计测试结果
SELECT 
    test_type AS '测试类型',
    COUNT(*) AS '总用例数',
    SUM(CASE WHEN status = 'PASS' THEN 1 ELSE 0 END) AS '通过数',
    SUM(CASE WHEN status = 'FAIL' THEN 1 ELSE 0 END) AS '失败数',
    CONCAT(ROUND(SUM(CASE WHEN status = 'PASS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2), '%') AS '通过率'
FROM test_results
GROUP BY test_type
UNION ALL
SELECT 
    '总计',
    COUNT(*),
    SUM(CASE WHEN status = 'PASS' THEN 1 ELSE 0 END),
    SUM(CASE WHEN status = 'FAIL' THEN 1 ELSE 0 END),
    CONCAT(ROUND(SUM(CASE WHEN status = 'PASS' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2), '%')
FROM test_results;

-- =====================================================
-- 第七步：显示失败用例详情
-- =====================================================
SELECT '=== 第七步：失败用例详情 ===' AS step;

SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '所有测试用例均通过！'
        ELSE CONCAT('有 ', COUNT(*), ' 个测试用例失败，请检查详情')
    END AS failure_summary
FROM test_results 
WHERE status = 'FAIL';

-- 显示失败用例的详细信息
SELECT 
    test_case AS '失败用例',
    expected_result AS '预期结果',
    actual_result AS '实际结果'
FROM test_results 
WHERE status = 'FAIL';

-- =====================================================
-- 测试执行结束
-- =====================================================
SELECT '========================================' AS separator;
SELECT 'rms_fx_yuliu_med 存储过程测试完成' AS test_end;
SELECT CONCAT('测试结束时间: ', NOW()) AS end_time;
SELECT '========================================' AS separator;

-- 清理临时表
DROP TEMPORARY TABLE IF EXISTS test_results;
