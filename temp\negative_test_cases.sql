-- =====================================================
-- 存储过程 rms_fx_yuliu_med 负向测试用例
-- 版本: MySQL 5.7
-- 创建日期: 2025-08-25
-- 说明: 测试存储过程在异常情况下的功能表现
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;

-- =====================================================
-- 负向测试用例 1: 停用药品检查
-- 测试场景: 使用已停用的药品
-- 预期结果: 存储过程直接退出，不产生任何分析结果
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_002';

-- 执行存储过程（TEST_DRUG_002是停用药品）
CALL rms_fx_yuliu_med('TEST_PRES_002', 'TEST_DRUG_002');

-- 验证结果：应该没有任何分析结果
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS - 停用药品正确退出，无分析结果'
        ELSE CONCAT('FAIL - 停用药品仍产生了', COUNT(*), '条分析结果')
    END AS test_result,
    COUNT(*) as result_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002';

-- 显示详细结果（应该为空）
SELECT '=== 负向测试用例1详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002';

-- =====================================================
-- 负向测试用例 2: 超量用药检查
-- 测试场景: 门诊用药总量超过最大发药量
-- 预期结果: 产生用药总量警告
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_002';

-- 执行存储过程（阿司匹林50片，超过最大30片）
CALL rms_fx_yuliu_med('TEST_PRES_002', 'TEST_DRUG_001');

-- 验证结果：应该产生用药总量警告
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS - 超量用药产生警告'
        ELSE 'FAIL - 超量用药未产生警告'
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002' 
  AND ywa = 'TEST_阿司匹林片' 
  AND wtcode = 'RLT047'
  AND title LIKE '%超出最大发药量%';

-- 显示详细结果
SELECT '=== 负向测试用例2详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002' AND wtcode = 'RLT047';

-- =====================================================
-- 负向测试用例 3: 外用软膏超量检查
-- 测试场景: 门诊软膏类药品超过5支
-- 预期结果: 产生软膏超量警告
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_002';

-- 执行存储过程（红霉素软膏6支，超过最大5支）
CALL rms_fx_yuliu_med('TEST_PRES_002', 'TEST_DRUG_003');

-- 验证结果：应该产生软膏超量警告
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS - 软膏超量产生警告'
        ELSE 'FAIL - 软膏超量未产生警告'
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002' 
  AND ywa = 'TEST_红霉素软膏' 
  AND wtcode = 'RLT047'
  AND title LIKE '%软膏%';

-- 显示详细结果
SELECT '=== 负向测试用例3详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002' AND ywa = 'TEST_红霉素软膏';

-- =====================================================
-- 负向测试用例 4: 科室限制检查
-- 测试场景: 在无权限科室使用受限药品
-- 预期结果: 产生科室限制警告
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_005';

-- 执行存储过程（儿科使用滴眼剂，但滴眼剂只能眼科使用）
CALL rms_fx_yuliu_med('TEST_PRES_005', 'TEST_DRUG_005');

-- 验证结果：应该产生科室限制警告
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS - 科室限制产生警告'
        ELSE 'FAIL - 科室限制未产生警告'
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_005' 
  AND ywa = 'TEST_氯霉素滴眼液' 
  AND wtcode = 'RLT048'
  AND title LIKE '%不能在该科室使用%';

-- 显示详细结果
SELECT '=== 负向测试用例4详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_005' AND wtcode = 'RLT048';

-- =====================================================
-- 负向测试用例 5: 医生限制检查
-- 测试场景: 无权限医生使用受限药品
-- 预期结果: 产生医生限制警告
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_005';

-- 执行存储过程（TEST_DOCT_004无权限使用抗菌药物）
CALL rms_fx_yuliu_med('TEST_PRES_005', 'TEST_DRUG_006');

-- 验证结果：应该产生医生限制警告
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS - 医生限制产生警告'
        ELSE 'FAIL - 医生限制未产生警告'
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_005' 
  AND ywa = 'TEST_头孢曲松钠注射液' 
  AND wtcode = 'RLT049'
  AND title LIKE '%不能该医生使用%';

-- 显示详细结果
SELECT '=== 负向测试用例5详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_005' AND wtcode = 'RLT049';

-- =====================================================
-- 负向测试用例 6: 禁用给药途径检查
-- 测试场景: 使用禁用的给药途径
-- 预期结果: 产生给药途径错误警告
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_002';

-- 执行存储过程（使用"取药用"给药途径）
CALL rms_fx_yuliu_med('TEST_PRES_002', 'TEST_DRUG_008');

-- 验证结果：应该产生给药途径错误警告
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS - 禁用给药途径产生警告'
        ELSE 'FAIL - 禁用给药途径未产生警告'
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002' 
  AND wtcode = 'RLT003'
  AND title LIKE '%给药途径错误%';

-- 显示详细结果
SELECT '=== 负向测试用例6详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002' AND wtcode = 'RLT003';

-- =====================================================
-- 负向测试用例 7: 自定义给药途径限制检查
-- 测试场景: 使用不符合自定义限制的给药途径
-- 预期结果: 产生给药途径问题警告
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_002';

-- 执行存储过程（阿司匹林使用注射途径，但只能口服）
CALL rms_fx_yuliu_med('TEST_PRES_002', 'TEST_DRUG_001');

-- 验证结果：应该产生给药途径问题警告
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS - 自定义给药途径限制产生警告'
        ELSE 'FAIL - 自定义给药途径限制未产生警告'
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002' 
  AND ywa = 'TEST_阿司匹林片' 
  AND wtcode = 'RLT026'
  AND title LIKE '%给药途径问题%';

-- 显示详细结果
SELECT '=== 负向测试用例7详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_002' AND wtcode = 'RLT026';

-- =====================================================
-- 负向测试用例 8: 历史相互作用检查
-- 测试场景: 当前药品与历史处方存在相互作用
-- 预期结果: 产生相互作用警告
-- =====================================================

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_001';

-- 执行存储过程（阿司匹林与历史处方中的卡托普利存在相互作用）
CALL rms_fx_yuliu_med('TEST_PRES_001', 'TEST_DRUG_001');

-- 验证结果：应该产生相互作用警告
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS - 历史相互作用产生警告'
        ELSE 'FAIL - 历史相互作用未产生警告'
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' 
  AND ywa = 'TEST_阿司匹林片' 
  AND wtcode = 'RLT014'
  AND title LIKE '%相互作用%';

-- 显示详细结果
SELECT '=== 负向测试用例8详细结果 ===' AS section;
SELECT code, ywa, ywb, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_001' AND wtcode = 'RLT014';

-- =====================================================
-- 负向测试用例 9: 边界值测试 - 软膏剂恰好5支
-- 测试场景: 软膏剂恰好5支（边界值）
-- 预期结果: 不产生警告（5支是允许的最大值）
-- =====================================================

-- 先插入一个5支软膏的处方药品记录
INSERT INTO rms_t_pres_med (code, med_name, his_code, ord_qty, ord_uom, freq, administer) 
VALUES ('TEST_PRES_BOUNDARY', 'TEST_红霉素软膏', 'TEST_DRUG_003', '5', '支', '02', 'TEST_ADM_004');

-- 插入对应的处方记录
INSERT INTO rms_t_pres (code, hosp_code, dept_code, doct_code, doct_name, hosp_flag, card_code, pres_id, pres_time, flag) 
VALUES ('TEST_PRES_BOUNDARY', 'TEST_HOSP', 'TEST_DEPT_001', 'TEST_DOCT_001', 'TEST_张医生', 'op', 'TEST_CARD_BOUNDARY', 'TEST_PRES_ID_BOUNDARY', NOW(), 0);

-- 清理之前的分析结果
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_BOUNDARY';

-- 执行存储过程
CALL rms_fx_yuliu_med('TEST_PRES_BOUNDARY', 'TEST_DRUG_003');

-- 验证结果：5支应该不产生警告
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS - 软膏5支边界值测试通过'
        ELSE 'FAIL - 软膏5支边界值产生了警告'
    END AS test_result,
    COUNT(*) as warning_count
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_BOUNDARY' 
  AND ywa = 'TEST_红霉素软膏' 
  AND wtcode = 'RLT047'
  AND title LIKE '%软膏%';

-- 显示详细结果
SELECT '=== 负向测试用例9详细结果 ===' AS section;
SELECT code, ywa, wtlvl, wtname, title, detail 
FROM rms_t_pres_fx 
WHERE code = 'TEST_PRES_BOUNDARY';

-- 清理边界值测试数据
DELETE FROM rms_t_pres_med WHERE code = 'TEST_PRES_BOUNDARY';
DELETE FROM rms_t_pres WHERE code = 'TEST_PRES_BOUNDARY';

-- =====================================================
-- 负向测试用例总结
-- =====================================================
SELECT '=== 负向测试用例总结 ===' AS section;
SELECT 
    '负向测试用例执行完成' AS message,
    '以上测试验证了存储过程在异常情况下的错误检测能力' AS description;
