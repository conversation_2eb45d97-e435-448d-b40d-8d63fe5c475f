-- =====================================================
-- 存储过程 rms_fx_yuliu_med 测试数据清理脚本
-- 版本: MySQL 5.7
-- 创建日期: 2025-08-25
-- 说明: 清理所有测试数据，确保不影响生产环境
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 清理开始
-- =====================================================
SELECT '========================================' AS separator;
SELECT '开始清理 rms_fx_yuliu_med 存储过程测试数据' AS cleanup_start;
SELECT CONCAT('清理开始时间: ', NOW()) AS start_time;
SELECT '========================================' AS separator;

-- =====================================================
-- 第一步：清理测试结果数据
-- =====================================================
SELECT '=== 第一步：清理测试结果数据 ===' AS step;

-- 清理处方分析结果
DELETE FROM rms_t_pres_fx WHERE code LIKE 'TEST_PRES_%';
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条处方分析结果数据') AS cleanup_result_1;

-- 清理可能的边界测试数据
DELETE FROM rms_t_pres_fx WHERE code = 'TEST_PRES_BOUNDARY';
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条边界测试结果数据') AS cleanup_result_2;

-- =====================================================
-- 第二步：清理处方相关数据
-- =====================================================
SELECT '=== 第二步：清理处方相关数据 ===' AS step;

-- 清理处方药品数据
DELETE FROM rms_t_pres_med WHERE code LIKE 'TEST_PRES_%';
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条处方药品数据') AS cleanup_result_3;

-- 清理边界测试的处方药品数据
DELETE FROM rms_t_pres_med WHERE code = 'TEST_PRES_BOUNDARY';
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条边界测试处方药品数据') AS cleanup_result_4;

-- 清理处方数据
DELETE FROM rms_t_pres WHERE code LIKE 'TEST_PRES_%';
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条处方数据') AS cleanup_result_5;

-- 清理边界测试的处方数据
DELETE FROM rms_t_pres WHERE code = 'TEST_PRES_BOUNDARY';
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条边界测试处方数据') AS cleanup_result_6;

-- =====================================================
-- 第三步：清理药品配置数据
-- =====================================================
SELECT '=== 第三步：清理药品配置数据 ===' AS step;

-- 清理相互作用数据
DELETE FROM rms_t_xhzy_edi WHERE id >= 900001 AND id <= 999999;
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条相互作用数据') AS cleanup_result_7;

-- 清理标准数据常规量结果
DELETE FROM rms_t_sda_cgl_result WHERE condition_id >= 900001 AND condition_id <= 999999;
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条标准数据常规量结果') AS cleanup_result_8;

-- 清理药品自定义给药途径数据
DELETE FROM rms_t_med_zdy_gytj WHERE id >= 900001 AND id <= 999999;
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条药品自定义给药途径数据') AS cleanup_result_9;

-- 清理药品医生限制数据
DELETE FROM rms_t_med_zdy_doct WHERE id >= 900001 AND id <= 999999;
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条药品医生限制数据') AS cleanup_result_10;

-- 清理药品科室限制数据
DELETE FROM rms_t_med_zdy_dept WHERE id >= 900001 AND id <= 999999;
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条药品科室限制数据') AS cleanup_result_11;

-- 清理药品最大发药量数据
DELETE FROM rms_t_drug_zdfyl WHERE drug_code LIKE 'TEST_DRUG_%';
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条药品最大发药量数据') AS cleanup_result_12;

-- =====================================================
-- 第四步：清理基础数据
-- =====================================================
SELECT '=== 第四步：清理基础数据 ===' AS step;

-- 清理药品标准数据映射
DELETE FROM rms_t_byyydzb WHERE yp_code LIKE 'TEST_DRUG_%';
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条药品标准数据映射') AS cleanup_result_13;

-- 清理基础药品数据
DELETE FROM rms_itf_hos_drug WHERE drug_id >= 900001 AND drug_id <= 999999;
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条基础药品数据') AS cleanup_result_14;

-- 清理频次数据
DELETE FROM rms_itf_hos_frequency WHERE freq_code IN ('01', '02', '03', '04', '13', '14', '15') AND hosp_code = 'TEST_HOSP';
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条频次数据') AS cleanup_result_15;

-- 清理给药途径数据
DELETE FROM rms_itf_hos_admin_route WHERE adm_code LIKE 'TEST_ADM_%';
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条给药途径数据') AS cleanup_result_16;

-- 清理科室信息数据
DELETE FROM rms_itf_hos_spec WHERE spec_code LIKE 'TEST_DEPT_%';
SELECT CONCAT('已清理 ', ROW_COUNT(), ' 条科室信息数据') AS cleanup_result_17;

-- =====================================================
-- 第五步：验证清理结果
-- =====================================================
SELECT '=== 第五步：验证清理结果 ===' AS step;

-- 检查是否还有残留的测试数据
SELECT 
    '处方分析结果' AS table_name,
    COUNT(*) AS remaining_count
FROM rms_t_pres_fx 
WHERE code LIKE 'TEST_PRES_%' OR code = 'TEST_PRES_BOUNDARY'

UNION ALL

SELECT 
    '处方药品数据',
    COUNT(*)
FROM rms_t_pres_med 
WHERE code LIKE 'TEST_PRES_%' OR code = 'TEST_PRES_BOUNDARY'

UNION ALL

SELECT 
    '处方数据',
    COUNT(*)
FROM rms_t_pres 
WHERE code LIKE 'TEST_PRES_%' OR code = 'TEST_PRES_BOUNDARY'

UNION ALL

SELECT 
    '基础药品数据',
    COUNT(*)
FROM rms_itf_hos_drug 
WHERE drug_code LIKE 'TEST_DRUG_%'

UNION ALL

SELECT 
    '药品标准数据映射',
    COUNT(*)
FROM rms_t_byyydzb 
WHERE yp_code LIKE 'TEST_DRUG_%'

UNION ALL

SELECT 
    '药品最大发药量',
    COUNT(*)
FROM rms_t_drug_zdfyl 
WHERE drug_code LIKE 'TEST_DRUG_%'

UNION ALL

SELECT 
    '药品科室限制',
    COUNT(*)
FROM rms_t_med_zdy_dept 
WHERE id >= 900001 AND id <= 999999

UNION ALL

SELECT 
    '药品医生限制',
    COUNT(*)
FROM rms_t_med_zdy_doct 
WHERE id >= 900001 AND id <= 999999

UNION ALL

SELECT 
    '药品给药途径限制',
    COUNT(*)
FROM rms_t_med_zdy_gytj 
WHERE id >= 900001 AND id <= 999999

UNION ALL

SELECT 
    '相互作用数据',
    COUNT(*)
FROM rms_t_xhzy_edi 
WHERE id >= 900001 AND id <= 999999

UNION ALL

SELECT 
    '给药途径数据',
    COUNT(*)
FROM rms_itf_hos_admin_route 
WHERE adm_code LIKE 'TEST_ADM_%'

UNION ALL

SELECT 
    '科室信息数据',
    COUNT(*)
FROM rms_itf_hos_spec 
WHERE spec_code LIKE 'TEST_DEPT_%'

UNION ALL

SELECT 
    '频次数据',
    COUNT(*)
FROM rms_itf_hos_frequency 
WHERE hosp_code = 'TEST_HOSP';

-- =====================================================
-- 第六步：清理结果汇总
-- =====================================================
SELECT '=== 第六步：清理结果汇总 ===' AS step;

-- 检查清理是否完全成功
SELECT 
    CASE 
        WHEN (
            SELECT COUNT(*) FROM rms_t_pres_fx WHERE code LIKE 'TEST_PRES_%' OR code = 'TEST_PRES_BOUNDARY'
        ) + (
            SELECT COUNT(*) FROM rms_t_pres_med WHERE code LIKE 'TEST_PRES_%' OR code = 'TEST_PRES_BOUNDARY'
        ) + (
            SELECT COUNT(*) FROM rms_t_pres WHERE code LIKE 'TEST_PRES_%' OR code = 'TEST_PRES_BOUNDARY'
        ) + (
            SELECT COUNT(*) FROM rms_itf_hos_drug WHERE drug_code LIKE 'TEST_DRUG_%'
        ) + (
            SELECT COUNT(*) FROM rms_t_byyydzb WHERE yp_code LIKE 'TEST_DRUG_%'
        ) + (
            SELECT COUNT(*) FROM rms_itf_hos_admin_route WHERE adm_code LIKE 'TEST_ADM_%'
        ) + (
            SELECT COUNT(*) FROM rms_itf_hos_spec WHERE spec_code LIKE 'TEST_DEPT_%'
        ) = 0 
        THEN 'SUCCESS - 所有测试数据已完全清理'
        ELSE 'WARNING - 仍有部分测试数据未清理完全，请手动检查'
    END AS cleanup_status;

-- =====================================================
-- 第七步：重要提醒
-- =====================================================
SELECT '=== 第七步：重要提醒 ===' AS step;

SELECT '清理完成后的重要提醒：' AS reminder_title;
SELECT '1. 请确认生产数据未受影响' AS reminder_1;
SELECT '2. 如需重新测试，请先执行 test_data_insert.sql' AS reminder_2;
SELECT '3. 建议在测试环境中执行，避免影响生产数据' AS reminder_3;
SELECT '4. 如发现数据异常，请立即联系数据库管理员' AS reminder_4;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 清理结束
-- =====================================================
SELECT '========================================' AS separator;
SELECT 'rms_fx_yuliu_med 存储过程测试数据清理完成' AS cleanup_end;
SELECT CONCAT('清理结束时间: ', NOW()) AS end_time;
SELECT '========================================' AS separator;
